import { RouteRecordRaw } from 'vue-router';
import LogRoute from './modules/log';  // 手动导入日志路由

// 导入所有模块路由
const modules = import.meta.glob('./modules/**/*.ts', { eager: true });

// 路由模块
const routeModuleList: RouteRecordRaw[] = [];

// 加入到路由集合中
Object.keys(modules).forEach((key) => {
  const mod = (modules[key] as any).default || {};
  const modList = Array.isArray(mod) ? [...mod] : [mod];
  routeModuleList.push(...modList);
});

// 确保日志路由被添加
if (!routeModuleList.some(route => route.name === 'Log')) {
  routeModuleList.push(LogRoute);
}

// 导出路由
export const asyncRoutes = [...routeModuleList];

// 导出基础路由
export const basicRoutes = [
  // 你的基础路由配置
];
