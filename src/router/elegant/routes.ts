/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { GeneratedRoute } from '@elegant-router/types';

export const generatedRoutes: GeneratedRoute[] = [
  {
    name: '403',
    path: '/403',
    component: 'layout.blank$view.403',
    meta: {
      title: '403',
      i18nKey: 'route.403',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '404',
    path: '/404',
    component: 'layout.blank$view.404',
    meta: {
      title: '404',
      i18nKey: 'route.404',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '500',
    path: '/500',
    component: 'layout.blank$view.500',
    meta: {
      title: '500',
      i18nKey: 'route.500',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'about',
    path: '/about',
    component: 'layout.base$view.about',
    meta: {
      title: 'about',
      i18nKey: 'route.about',
      order: 999,
      icon: 'material-symbols:help-outline-rounded'
    }
  },
  {
    name: 'group',
    path: '/group',
    component: 'layout.base$view.group',
    meta: {
      title: 'group',
      i18nKey: 'route.group',
      order: 30,
      icon: 'material-symbols:group-work-outline',
      keepAlive: false
    }
  },
  {
    name: 'home',
    path: '/home',
    component: 'layout.base$view.home',
    meta: {
      title: 'home',
      i18nKey: 'route.home',
      icon: 'material-symbols:dashboard-outline-rounded',
      order: 1,
      keepAlive: false
    }
  },
  {
    name: 'iframe-page',
    path: '/iframe-page/:url',
    component: 'layout.base$view.iframe-page',
    props: true,
    meta: {
      title: 'iframe-page',
      i18nKey: 'route.iframe-page',
      constant: true,
      hideInMenu: true,
      keepAlive: true
    }
  },
  {
    name: 'job',
    path: '/job',
    component: 'layout.base',
    meta: {
      title: 'job',
      i18nKey: 'route.job',
      order: 50,
      icon: 'eos-icons:cronjob',
      keepAlive: false
    },
    children: [
      {
        name: 'job_batch',
        path: '/job/batch',
        component: 'view.job_batch',
        meta: {
          title: 'job_batch',
          i18nKey: 'route.job_batch',
          icon: 'carbon:batch-job',
          order: 20,
          keepAlive: false
        }
      },
      {
        name: 'job_task',
        path: '/job/task',
        component: 'view.job_task',
        meta: {
          title: 'job_task',
          i18nKey: 'route.job_task',
          icon: 'octicon:tasklist',
          order: 10,
          keepAlive: false
        }
      }
    ]
  },
  {
    name: 'log',
    path: '/log',
    component: 'layout.blank$view.log',
    meta: {
      title: 'log',
      i18nKey: 'route.log',
      icon: 'carbon:batch-job',
      hideInMenu: true
    }
  },
  {
    name: 'login',
    path: '/login/:module(pwd-login)?',
    component: 'layout.blank$view.login',
    props: true,
    meta: {
      title: 'login',
      i18nKey: 'route.login',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'namespace',
    path: '/namespace',
    component: 'layout.base$view.namespace',
    meta: {
      title: 'namespace',
      i18nKey: 'route.namespace',
      icon: 'eos-icons:namespace',
      order: 20,
      roles: ['R_ADMIN'],
      keepAlive: false
    }
  },
  {
    name: 'notify',
    path: '/notify',
    component: 'layout.base',
    meta: {
      title: 'notify',
      i18nKey: 'route.notify',
      order: 100,
      icon: 'material-symbols:notifications-active-outline-rounded',
      keepAlive: false
    },
    children: [
      {
        name: 'notify_config',
        path: '/notify/config',
        component: 'view.notify_config',
        meta: {
          title: 'notify_config',
          i18nKey: 'route.notify_config',
          icon: 'cbi:scene-dynamic',
          keepAlive: false
        }
      },
      {
        name: 'notify_recipient',
        path: '/notify/recipient',
        component: 'view.notify_recipient',
        meta: {
          title: 'notify_recipient',
          i18nKey: 'route.notify_recipient',
          icon: 'fluent:people-call-20-filled',
          keepAlive: false
        }
      }
    ]
  },
  {
    name: 'pods',
    path: '/pods',
    component: 'layout.base$view.pods',
    meta: {
      title: 'pods',
      i18nKey: 'route.pods',
      icon: 'ant-design:database-outlined',
      order: 10,
      keepAlive: false
    }
  },
  {
    name: 'retry',
    path: '/retry',
    component: 'layout.base',
    meta: {
      title: 'retry',
      i18nKey: 'route.retry',
      order: 70,
      icon: 'carbon:retry-failed',
      keepAlive: false
    },
    children: [
      {
        name: 'retry_dead-letter',
        path: '/retry/dead-letter',
        component: 'view.retry_dead-letter',
        meta: {
          title: 'retry_dead-letter',
          i18nKey: 'route.retry_dead-letter',
          icon: 'streamline:interface-arrows-synchronize-warning-arrow-fail-notification-sync-warning-failure-synchronize-error',
          order: 30,
          keepAlive: false
        }
      },
      {
        name: 'retry_info',
        path: '/retry/info',
        component: 'view.retry_info',
        meta: {
          title: 'retry_info',
          i18nKey: 'route.retry_info',
          icon: 'octicon:tasklist'
        }
      },
      {
        name: 'retry_scene',
        path: '/retry/scene',
        component: 'view.retry_scene',
        meta: {
          title: 'retry_scene',
          i18nKey: 'route.retry_scene',
          icon: 'cbi:scene-dynamic',
          order: 1,
          keepAlive: false
        }
      },
      {
        name: 'retry_task',
        path: '/retry/task',
        component: 'view.retry_task',
        meta: {
          title: 'retry_task',
          i18nKey: 'route.retry_task',
          icon: 'carbon:batch-job',
          keepAlive: false
        }
      }
    ]
  },
  {
    name: 'user',
    path: '/user',
    component: 'layout.base',
    meta: {
      title: 'user',
      i18nKey: 'route.user',
      order: 900,
      icon: 'material-symbols:manage-accounts',
      roles: ['R_ADMIN'],
      keepAlive: false
    },
    children: [
      {
        name: 'user_manager',
        path: '/user/manager',
        component: 'view.user_manager',
        meta: {
          title: 'user_manager',
          i18nKey: 'route.user_manager',
          icon: 'streamline:interface-user-multiple-close-geometric-human-multiple-person-up-user',
          order: 900,
          roles: ['R_ADMIN'],
          keepAlive: false
        }
      }
    ]
  },
  {
    name: 'workflow',
    path: '/workflow',
    component: 'layout.base',
    meta: {
      title: 'workflow',
      i18nKey: 'route.workflow',
      order: 60,
      icon: 'lucide:workflow',
      keepAlive: false
    },
    children: [
      {
        name: 'workflow_batch',
        path: '/workflow/batch',
        component: 'view.workflow_batch',
        meta: {
          title: 'workflow_batch',
          i18nKey: 'route.workflow_batch',
          icon: 'carbon:batch-job',
          order: 10,
          keepAlive: false
        }
      },
      {
        name: 'workflow_form',
        path: '/workflow/form',
        meta: {
          title: 'workflow_form',
          hideInMenu: true,
          i18nKey: 'route.workflow_form'
        },
        children: [
          {
            name: 'workflow_form_add',
            path: '/workflow/form/add',
            component: 'view.workflow_form_add',
            meta: {
              hideInMenu: true,
              title: 'workflow_form_add',
              i18nKey: 'route.workflow_form_add'
            }
          },
          {
            name: 'workflow_form_batch',
            path: '/workflow/form/batch',
            component: 'view.workflow_form_batch',
            meta: {
              hideInMenu: true,
              title: 'workflow_form_batch',
              i18nKey: 'route.workflow_form_batch'
            }
          },
          {
            name: 'workflow_form_copy',
            path: '/workflow/form/copy',
            component: 'view.workflow_form_copy',
            meta: {
              title: 'workflow_form_copy',
              hideInMenu: true,
              i18nKey: 'route.workflow_form_copy'
            }
          },
          {
            name: 'workflow_form_detail',
            path: '/workflow/form/detail',
            component: 'view.workflow_form_detail',
            meta: {
              title: 'workflow_form_detail',
              hideInMenu: true,
              i18nKey: 'route.workflow_form_detail'
            }
          },
          {
            name: 'workflow_form_edit',
            path: '/workflow/form/edit',
            component: 'view.workflow_form_edit',
            meta: {
              title: 'workflow_form_edit',
              hideInMenu: true,
              i18nKey: 'route.workflow_form_edit'
            }
          }
        ]
      },
      {
        name: 'workflow_task',
        path: '/workflow/task',
        component: 'view.workflow_task',
        meta: {
          title: 'workflow_task',
          i18nKey: 'route.workflow_task',
          icon: 'octicon:tasklist',
          order: 1,
          keepAlive: false
        }
      }
    ]
  }
];
