import { request } from '@/service/request';

export interface LoginLogParams {
  page: number;
  pageSize: number;
  khid?: string;
  nsrsbh?: string;
  startTime?: number;
  endTime?: number;
}

export interface LoginLogItem {
  khid: string;
  nsrsbh: string;
  retcode: string;
  retmsg: string;
  createTime: string;
  updateTime: string;
}

export interface LoginLogResponse {
  list: LoginLogItem[];
  total: number;
}

export function fetchLoginLogs(params: LoginLogParams) {
  return request<LoginLogResponse>({
    url: '/api/log/login/list',
    method: 'get',
    params
  });
}
